# 服务器类型支持功能更新

## 更新概述

本次更新扩展了系统功能，从原来只支持Vultr裸机服务器，扩展为同时支持裸机(bare metal)和VPS实例(instances)。

## 主要变更

### 1. 数据库模型更新
- **文件**: `backend/models/server.py`
- **变更**: 
  - 添加了`ServerType`枚举类型
  - 在`Server`模型中添加了`server_type`字段
  - 更新了`__repr__`方法以显示服务器类型

### 2. Vultr服务扩展
- **文件**: `backend/services/vultr_service.py`
- **变更**:
  - 添加了instances相关的API方法：
    - `list_instances()` - 获取VPS实例列表
    - `get_instance()` - 获取单个VPS实例详情
    - `reboot_instance()` - 重启VPS实例
    - `check_instance_status()` - 检查VPS实例状态
  - 添加了通用方法：
    - `reboot_server()` - 根据服务器类型重启服务器
    - `check_server_status_by_type()` - 根据服务器类型检查状态
    - `get_server_info()` - 根据服务器类型获取服务器信息

### 3. 服务器同步逻辑更新
- **文件**: `backend/services/server_service.py`
- **变更**:
  - 重写了`sync_servers_from_vultr()`方法，同时同步裸机和实例
  - 添加了`_sync_servers_by_type()`辅助方法
  - 更新了`get_servers()`方法，支持按类型过滤和搜索

### 4. API接口增强
- **文件**: `backend/api/server.py`
- **变更**:
  - 更新了`get_servers`接口，添加了`server_type`和`search`参数
  - 支持按服务器类型过滤
  - 支持IP地址、ID和标签的模糊搜索

### 5. 前端页面更新

#### Dashboard组件
- **文件**: `frontend/src/components/Dashboard.tsx`
- **变更**:
  - 添加了搜索和过滤功能
  - 在服务器列表中显示服务器类型
  - 支持按类型过滤和关键词搜索

#### Bindings组件
- **文件**: `frontend/src/components/Bindings.tsx`
- **变更**:
  - 在绑定关系表格中添加了服务器ID列
  - 更新了服务器搜索过滤逻辑，支持ID搜索
  - 改进了搜索提示文本

#### API服务
- **文件**: `frontend/src/services/api.ts`
- **变更**:
  - 更新了`Server`接口，添加了`server_type`字段
  - 更新了`getServers`方法，支持类型过滤和搜索参数

### 6. 重启监控逻辑调整
- **文件**: `backend/tasks/restart_monitor.py`
- **变更**:
  - 添加了服务器类型检查逻辑
  - 使用`check_server_status_by_type()`方法根据类型检查状态

- **文件**: `backend/services/message_service.py`
- **变更**:
  - 更新了重启流程，使用`reboot_server()`方法根据类型重启

### 7. Schema更新
- **文件**: `backend/schemas/server.py`
- **变更**:
  - 在所有相关Schema中添加了`server_type`字段
  - 导入了`ServerType`枚举

## 数据库迁移

### 迁移脚本
- **文件**: `backend/migrations/add_server_type.sql`
- **内容**: 添加`server_type`字段并设置默认值

### 执行步骤
```sql
-- 连接到数据库并执行
mysql -u your_username -p your_database < backend/migrations/add_server_type.sql
```

## 测试

### 测试脚本
- **文件**: `backend/test_server_types.py`
- **功能**: 
  - 测试Vultr API功能
  - 测试数据库操作
  - 测试同步功能
  - 验证所有新功能

### 运行测试
```bash
cd backend
python test_server_types.py
```

## 部署步骤

1. **数据库迁移**
   ```bash
   mysql -u your_username -p your_database < backend/migrations/add_server_type.sql
   ```

2. **重启后端服务**
   ```bash
   # 停止现有服务
   pkill -f "uvicorn main:app"
   pkill -f "celery"
   
   # 启动新服务
   uvicorn main:app --reload --host 0.0.0.0 --port 8000 &
   python start_celery.py worker &
   python start_celery.py beat &
   ```

3. **重启前端服务**
   ```bash
   # 如果使用npm
   cd frontend
   npm run dev
   
   # 或者使用Python服务器
   python -m http.server 8080 --directory src
   ```

4. **验证功能**
   - 访问前端管理界面
   - 测试服务器同步功能
   - 验证搜索和过滤功能
   - 测试重启功能（谨慎操作）

## 新功能使用说明

### 1. 服务器类型显示
- 在Dashboard页面，服务器列表现在显示服务器类型（裸机/实例）
- 使用不同颜色的徽章区分类型

### 2. 搜索和过滤
- 支持按服务器类型过滤（所有类型/裸机/实例）
- 支持关键词搜索（IP地址、服务器ID、标签）
- 实时搜索，无需点击搜索按钮

### 3. 绑定关系管理
- 绑定关系表格现在显示服务器ID
- 服务器选择下拉框支持ID搜索
- 改进的过滤提示

### 4. 自动同步
- 定时任务现在会同时同步裸机和实例
- 手动同步也支持两种类型
- 同步日志会显示具体的同步数量

## 注意事项

1. **向后兼容性**: 现有的裸机服务器会自动标记为`bare_metal`类型
2. **API权限**: 确保Vultr API密钥有访问instances的权限
3. **测试环境**: 建议先在测试环境验证所有功能
4. **监控**: 部署后监控日志，确保同步和重启功能正常

## 故障排除

### 常见问题

1. **同步失败**
   - 检查Vultr API密钥权限
   - 查看应用日志中的错误信息

2. **前端显示异常**
   - 清除浏览器缓存
   - 检查API响应格式

3. **重启功能异常**
   - 确认服务器类型正确
   - 检查Vultr API调用日志

### 日志查看
```bash
# 查看应用日志
tail -f backend/logs/lark_robot.log

# 查看Celery日志
tail -f celery.log
```

## 联系支持

如有问题，请查看日志文件或联系开发团队。
