"""重启监控任务"""
import asyncio
from sqlalchemy.orm import sessionmaker
from tasks.celery_app import celery_app
from database import engine
from services.vultr_service import vultr_service
from services.lark_service import lark_service
from models.log import OperationLog, OperationStatus
from models.server import Server
from config import settings
from utils.logger import app_logger

# 创建数据库会话
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Lark消息模板
MESSAGES = {
    "restart_success": "您的远程电脑重启完成啦",
    "restart_failed": "您的远程电脑重启失败，请联系管理员"
}

async def monitor_restart_task(operation_log_id: int, vultr_id: str, lark_user_id: str):
    """监控重启任务（异步函数，用于后台任务）"""
    db = SessionLocal()
    try:
        app_logger.info(f"开始监控服务器重启状态: {vultr_id}")
        
        # 获取操作日志
        operation_log = db.query(OperationLog).filter(OperationLog.id == operation_log_id).first()
        if not operation_log:
            app_logger.error(f"操作日志不存在: {operation_log_id}")
            return

        # 获取服务器信息以确定服务器类型
        server = db.query(Server).filter(Server.vultr_id == vultr_id).first()
        if not server:
            app_logger.error(f"服务器不存在: {vultr_id}")
            operation_log.status = OperationStatus.FAILED
            operation_log.error_message = f"服务器不存在: {vultr_id}"
            db.commit()
            return
        
        # 监控重启状态，最多检查6次，每次间隔10秒
        max_retries = settings.RESTART_MAX_RETRIES
        interval = settings.RESTART_MONITOR_INTERVAL_SECONDS
        
        for attempt in range(max_retries):
            app_logger.info(f"检查服务器状态，第 {attempt + 1}/{max_retries} 次: {vultr_id}")
            
            # 根据服务器类型获取服务器状态
            status = await vultr_service.check_server_status_by_type(vultr_id, server.server_type)
            
            if status:
                app_logger.info(f"服务器 {vultr_id} 当前状态: {status}")
                
                # 检查是否重启完成（状态为active表示重启完成）
                if status.lower() == "active":
                    # 重启成功
                    operation_log.status = OperationStatus.SUCCESS
                    operation_log.result = "服务器重启成功"
                    db.commit()
                    
                    app_logger.info(f"服务器 {vultr_id} 重启成功")
                    await lark_service.send_text_message(lark_user_id, MESSAGES["restart_success"])
                    return
            
            # 如果不是最后一次尝试，等待一段时间
            if attempt < max_retries - 1:
                await asyncio.sleep(interval)
        
        # 所有尝试都失败了
        operation_log.status = OperationStatus.FAILED
        operation_log.error_message = f"重启状态检查超时，尝试了 {max_retries} 次"
        db.commit()
        
        app_logger.warning(f"服务器 {vultr_id} 重启状态检查超时")
        await lark_service.send_text_message(lark_user_id, MESSAGES["restart_failed"])
        
    except Exception as e:
        app_logger.error(f"监控重启任务异常: {e}")
        
        # 更新日志为失败状态
        try:
            operation_log = db.query(OperationLog).filter(OperationLog.id == operation_log_id).first()
            if operation_log:
                operation_log.status = OperationStatus.FAILED
                operation_log.error_message = f"监控重启任务异常: {str(e)}"
                db.commit()
        except:
            pass
        
        # 发送失败消息
        try:
            await lark_service.send_text_message(lark_user_id, MESSAGES["restart_failed"])
        except:
            pass
        
    finally:
        db.close()

@celery_app.task(bind=True)
def monitor_restart_celery_task(self, operation_log_id: int, vultr_id: str, lark_user_id: str):
    """Celery重启监控任务包装器"""
    try:
        # 在新的事件循环中运行异步任务
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(monitor_restart_task(operation_log_id, vultr_id, lark_user_id))
        loop.close()
        
        return {"status": "completed", "operation_log_id": operation_log_id}
        
    except Exception as e:
        app_logger.error(f"Celery重启监控任务失败: {e}")
        raise self.retry(exc=e, countdown=30, max_retries=2)
