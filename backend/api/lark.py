"""Lark Webhook API路由"""
import json
import asyncio
from fastapi import APIRouter, Request, HTTPException, status, Depends, BackgroundTasks, Query
from sqlalchemy.orm import Session
from typing import Optional
import lark_oapi as lark
from lark_oapi.api.im.v1 import *

from database import get_db
from services.lark_service import lark_service
from services.message_service import MessageService
from utils.logger import app_logger
from api.auth import get_current_user
from config import settings

router = APIRouter()

def do_p2_im_message_receive_v1(req: lark.im.v1.P2ImMessageReceiveV1) -> str:
    """处理即时消息接收事件（包括P2P和群组@消息）"""
    app_logger.info(f"收到即时消息事件: {lark.JSON.marshal(req)}")

    lark_user_id = None
    try:
        # 获取消息信息
        event = req.event
        message = event.message
        sender = event.sender

        lark_user_id = sender.sender_id.user_id
        msg_type = message.message_type
        content = message.content
        chat_type = message.chat_type  # 用于区分消息类型

        # 根据chat_type判断是P2P消息还是群组消息
        if chat_type == "p2p":
            app_logger.info(f"收到P2P即时消息 - 用户: {lark_user_id}, 类型: {msg_type}")
            message_type = "p2p"
        else:
            # 群组消息或其他类型消息
            app_logger.info(f"收到群组消息 - 用户: {lark_user_id}, 类型: {msg_type}, 聊天类型: {chat_type}")
            message_type = "group"

        # 获取数据库会话和后台任务上下文
        db, background_tasks = MessageService.get_context()

        if db is None or background_tasks is None:
            app_logger.error(f"{message_type}消息处理失败：缺少数据库会话或后台任务上下文")
            return ""

        # 使用MessageService中的通用验证和处理方法
        asyncio.create_task(
            MessageService.validate_and_process_message(
                lark_user_id, msg_type, content, message_type, db, background_tasks
            )
        )
        return ""

    except Exception as e:
        app_logger.error(f"处理消息事件异常: {e}", exc_info=True)
        # 尝试发送错误消息给用户
        try:
            if lark_user_id:
                # 确定消息类型用于错误回复
                message_type = "p2p"  # 默认为p2p
                try:
                    if req.event and req.event.message and req.event.message.chat_type != "p2p":
                        message_type = "group"
                except:
                    pass
                asyncio.create_task(MessageService.send_invalid_input_message(lark_user_id, message_type))
        except Exception as send_error:
            app_logger.error(f"发送错误消息失败: {send_error}")
        return ""

# 创建EventDispatcherHandler - 只注册统一的消息接收处理器
handler = lark.EventDispatcherHandler.builder(settings.LARK_ENCRYPT_KEY, settings.LARK_VERIFY_TOKEN, lark.LogLevel.DEBUG) \
    .register_p2_im_message_receive_v1(do_p2_im_message_receive_v1) \
    .build()

@router.post("/lark")
async def lark_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Lark Webhook接口 - 使用EventDispatcherHandler处理"""
    try:
        # 获取请求体
        body = await request.body()
        body_str = body.decode('utf-8')
        
        # 获取请求头
        headers = request.headers
        timestamp = headers.get("X-Lark-Request-Timestamp", "")
        nonce = headers.get("X-Lark-Request-Nonce", "")
        signature = headers.get("X-Lark-Signature", "")
        
        # 设置MessageService上下文供事件处理函数使用
        MessageService.set_context(db, background_tasks)
        
        # 构造RawRequest对象
        raw_request = lark.RawRequest()
        raw_request.headers = {
            "X-Lark-Request-Timestamp": timestamp,
            "X-Lark-Request-Nonce": nonce,
            "X-Lark-Signature": signature
        }
        raw_request.body = body_str.encode('utf-8') if isinstance(body_str, str) else body_str
        raw_request.uri = str(request.url)
        
        # 使用EventDispatcherHandler处理请求
        raw_response = handler.do(raw_request)
        result = raw_response.content if raw_response else None
        
        # 如果result不为空，说明是URL验证请求
        if result:
            try:
                # 尝试解析为JSON
                result_data = json.loads(result)
                return result_data
            except json.JSONDecodeError:
                # 如果不是JSON，直接返回字符串
                return {"result": result}
        
        return {"status": "ok"}
        
    except Exception as e:
        app_logger.error(f"处理Lark Webhook异常: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="内部服务器错误")

@router.get("/search-user")
async def search_lark_user_by_email(
    email: str = Query(..., description="邮箱地址"),
    current_user = Depends(get_current_user)
):
    """根据邮箱搜索Lark用户"""
    try:
        user_info = await lark_service.search_user_by_email(email)
        if not user_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到对应的Lark用户"
            )
        
        return {
            "user_id": user_info.get("user_id"),
            "name": user_info.get("name"),
            "email": email,
            "avatar": user_info.get("avatar", {}).get("avatar_72") if user_info.get("avatar") else None
        }
    except HTTPException:
        raise
    except Exception as e:
        app_logger.error(f"搜索Lark用户异常: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="搜索用户失败"
        )
