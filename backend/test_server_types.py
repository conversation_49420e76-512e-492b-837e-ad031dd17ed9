#!/usr/bin/env python3
"""
测试服务器类型功能的脚本
用于验证裸机和实例的同步、重启等功能是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import SessionLocal
from services.vultr_service import vultr_service
from services.server_service import ServerService
from models.server import Server, ServerType
from utils.logger import app_logger

async def test_vultr_api():
    """测试Vultr API功能"""
    print("=== 测试Vultr API功能 ===")
    
    # 测试获取裸机列表
    print("1. 测试获取裸机列表...")
    bare_metals = await vultr_service.list_bare_metals()
    print(f"   获取到 {len(bare_metals)} 台裸机")
    
    # 测试获取实例列表
    print("2. 测试获取实例列表...")
    instances = await vultr_service.list_instances()
    print(f"   获取到 {len(instances)} 台实例")
    
    # 测试通用重启方法（如果有服务器的话）
    if bare_metals:
        server_id = bare_metals[0].get('id')
        print(f"3. 测试裸机重启方法（服务器ID: {server_id}）...")
        # 注意：这里只是测试方法调用，不会真正重启
        # result = await vultr_service.reboot_server(server_id, ServerType.BARE_METAL)
        print("   跳过实际重启测试（避免影响生产环境）")
    
    if instances:
        server_id = instances[0].get('id')
        print(f"4. 测试实例重启方法（服务器ID: {server_id}）...")
        # 注意：这里只是测试方法调用，不会真正重启
        # result = await vultr_service.reboot_server(server_id, ServerType.INSTANCE)
        print("   跳过实际重启测试（避免影响生产环境）")

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    db = SessionLocal()
    try:
        # 测试查询服务器
        print("1. 测试查询所有服务器...")
        servers, total = ServerService.get_servers(db)
        print(f"   数据库中共有 {total} 台服务器")
        
        # 按类型统计
        bare_metal_count = db.query(Server).filter(Server.server_type == ServerType.BARE_METAL).count()
        instance_count = db.query(Server).filter(Server.server_type == ServerType.INSTANCE).count()
        print(f"   其中裸机: {bare_metal_count} 台，实例: {instance_count} 台")
        
        # 测试搜索功能
        print("2. 测试搜索功能...")
        if servers:
            # 测试IP搜索
            test_server = servers[0]
            if test_server.ip_address:
                search_results, _ = ServerService.get_servers(db, search=test_server.ip_address[:5])
                print(f"   IP搜索结果: {len(search_results)} 台服务器")
            
            # 测试ID搜索
            search_results, _ = ServerService.get_servers(db, search=test_server.vultr_id[:5])
            print(f"   ID搜索结果: {len(search_results)} 台服务器")
        
        # 测试类型过滤
        print("3. 测试类型过滤...")
        bare_metal_servers, bm_total = ServerService.get_servers(db, server_type=ServerType.BARE_METAL)
        instance_servers, inst_total = ServerService.get_servers(db, server_type=ServerType.INSTANCE)
        print(f"   裸机过滤结果: {bm_total} 台")
        print(f"   实例过滤结果: {inst_total} 台")
        
    finally:
        db.close()

async def test_sync_functionality():
    """测试同步功能"""
    print("\n=== 测试同步功能 ===")
    
    db = SessionLocal()
    try:
        print("1. 执行服务器同步...")
        synced_count = await ServerService.sync_servers_from_vultr(db)
        print(f"   同步了 {synced_count} 台服务器")
        
        # 检查同步后的数据
        print("2. 检查同步后的数据...")
        servers, total = ServerService.get_servers(db)
        bare_metal_count = db.query(Server).filter(Server.server_type == ServerType.BARE_METAL).count()
        instance_count = db.query(Server).filter(Server.server_type == ServerType.INSTANCE).count()
        
        print(f"   总服务器数: {total}")
        print(f"   裸机数量: {bare_metal_count}")
        print(f"   实例数量: {instance_count}")
        
    finally:
        db.close()

def print_summary():
    """打印测试总结"""
    print("\n=== 功能更新总结 ===")
    print("✅ 数据库模型已更新，添加了server_type字段")
    print("✅ Vultr服务已扩展，支持instances API")
    print("✅ 服务器同步逻辑已更新，同时同步裸机和实例")
    print("✅ API接口已增强，支持类型过滤和搜索")
    print("✅ 前端页面已更新，显示服务器类型和增强搜索")
    print("✅ 定时任务已更新，支持同时同步两种类型")
    print("✅ 重启监控逻辑已调整，根据类型调用不同API")
    print("\n📋 需要手动执行的步骤:")
    print("1. 运行数据库迁移脚本: backend/migrations/add_server_type.sql")
    print("2. 重启后端服务以应用代码更改")
    print("3. 重启前端服务以应用界面更改")
    print("4. 测试同步功能和重启功能")

async def main():
    """主测试函数"""
    print("开始测试服务器类型功能...")
    
    try:
        # 测试Vultr API
        await test_vultr_api()
        
        # 测试数据库操作
        test_database_operations()
        
        # 测试同步功能
        await test_sync_functionality()
        
        # 打印总结
        print_summary()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        app_logger.error(f"测试异常: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
