"""Vultr API服务"""
import httpx
import json
from typing import List, Dict, Optional
from config import settings
from utils.logger import app_logger

class VultrService:
    """Vultr API服务类"""
    
    def __init__(self):
        self.api_key = settings.VULTR_API_KEY
        self.base_url = settings.VULTR_API_BASE_URL
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    async def list_bare_metals(self) -> List[Dict]:
        """获取裸机实例列表（支持分页）"""
        all_bare_metals = []
        cursor = None
        
        try:
            async with httpx.AsyncClient() as client:
                while True:
                    # 构建请求参数
                    params = {"per_page": 100}  # 每页最多100个
                    if cursor:
                        params["cursor"] = cursor
                    
                    response = await client.get(
                        f"{self.base_url}/v2/bare-metals",
                        headers=self.headers,
                        params=params,
                        timeout=30.0
                    )
                    response.raise_for_status()
                    data = response.json()
                    
                    # 获取当前页的数据
                    bare_metals = data.get("bare_metals", [])
                    all_bare_metals.extend(bare_metals)
                    
                    # 检查是否还有下一页
                    meta = data.get("meta", {})
                    next_cursor = meta.get("next")
                    
                    if not next_cursor:
                        # 没有下一页，跳出循环
                        break
                    
                    cursor = next_cursor
                    app_logger.info(f"获取到 {len(bare_metals)} 台裸机，继续获取下一页...")
                
                app_logger.info(f"总共获取到 {len(all_bare_metals)} 台Vultr裸机")
                return all_bare_metals
                
        except Exception as e:
            app_logger.error(f"获取Vultr裸机列表失败: {e}")
            return []
    
    async def get_bare_metal(self, baremetal_id: str) -> Optional[Dict]:
        """获取单个裸机实例详情"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/v2/bare-metals/{baremetal_id}",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                data = response.json()
                return data.get("bare_metal")
        except Exception as e:
            app_logger.error(f"获取Vultr裸机详情失败 (ID: {baremetal_id}): {e}")
            return None
    
    async def reboot_bare_metal(self, baremetal_id: str) -> bool:
        """重启裸机实例"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/v2/bare-metals/{baremetal_id}/reboot",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                app_logger.info(f"成功发送重启命令到Vultr服务器: {baremetal_id}")
                return True
        except Exception as e:
            app_logger.error(f"重启Vultr裸机失败 (ID: {baremetal_id}): {e}")
            return False
    
    async def check_server_status(self, baremetal_id: str) -> Optional[str]:
        """检查服务器状态"""
        server_info = await self.get_bare_metal(baremetal_id)
        if server_info:
            return server_info.get("status")
        return None
    
    async def list_instances(self) -> List[Dict]:
        """获取VPS实例列表（支持分页）"""
        all_instances = []
        cursor = None
        
        try:
            async with httpx.AsyncClient() as client:
                while True:
                    # 构建请求参数
                    params = {"per_page": 100}  # 每页最多100个
                    if cursor:
                        params["cursor"] = cursor
                    
                    response = await client.get(
                        f"{self.base_url}/v2/instances",
                        headers=self.headers,
                        params=params,
                        timeout=30.0
                    )
                    response.raise_for_status()
                    data = response.json()
                    
                    # 获取当前页的数据
                    instances = data.get("instances", [])
                    all_instances.extend(instances)
                    
                    # 检查是否还有下一页
                    meta = data.get("meta", {})
                    next_cursor = meta.get("next")
                    
                    if not next_cursor:
                        # 没有下一页，跳出循环
                        break
                    
                    cursor = next_cursor
                    app_logger.info(f"获取到 {len(instances)} 台VPS实例，继续获取下一页...")
                
                app_logger.info(f"总共获取到 {len(all_instances)} 台Vultr VPS实例")
                return all_instances
                
        except Exception as e:
            app_logger.error(f"获取Vultr VPS实例列表失败: {e}")
            return []
    
    async def get_instance(self, instance_id: str) -> Optional[Dict]:
        """获取单个VPS实例详情"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.base_url}/v2/instances/{instance_id}",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                data = response.json()
                return data.get("instance")
        except Exception as e:
            app_logger.error(f"获取Vultr VPS实例详情失败 (ID: {instance_id}): {e}")
            return None
    
    async def reboot_instance(self, instance_id: str) -> bool:
        """重启VPS实例"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/v2/instances/{instance_id}/reboot",
                    headers=self.headers,
                    timeout=30.0
                )
                response.raise_for_status()
                app_logger.info(f"成功发送重启命令到Vultr VPS实例: {instance_id}")
                return True
        except Exception as e:
            app_logger.error(f"重启Vultr VPS实例失败 (ID: {instance_id}): {e}")
            return False
    
    async def check_instance_status(self, instance_id: str) -> Optional[str]:
        """检查VPS实例状态"""
        instance_info = await self.get_instance(instance_id)
        if instance_info:
            return instance_info.get("status")
        return None

# 创建全局实例
vultr_service = VultrService()
