-- 修复服务器类型枚举值，使其与SQLAlchemy期望的大写值匹配
-- 这个脚本将枚举值从小写改为大写

USE lark_robot;

-- 修改枚举定义，将小写值改为大写值
ALTER TABLE servers MODIFY COLUMN server_type ENUM('BARE_METAL', 'INSTANCE') NOT NULL DEFAULT 'BARE_METAL' COMMENT '服务器类型';

-- 更新现有数据，将小写值转换为大写值
UPDATE servers SET server_type = 'BARE_METAL' WHERE server_type = 'bare_metal';
UPDATE servers SET server_type = 'INSTANCE' WHERE server_type = 'instance';

-- 验证更新结果
SELECT server_type, COUNT(*) as count FROM servers GROUP BY server_type;

-- 显示更新后的表结构
SHOW CREATE TABLE servers;