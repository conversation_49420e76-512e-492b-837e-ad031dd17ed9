#!/usr/bin/env python3
"""更新数据库中的枚举值脚本"""
import sys
sys.path.append('/app')

from sqlalchemy import create_engine, text
from config import settings

def update_enum_values():
    """更新数据库中的枚举值为大写"""
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        # 更新operation_type字段
        result1 = conn.execute(text(
            "UPDATE operation_logs SET operation_type = UPPER(operation_type)"
        ))
        print(f"更新了 {result1.rowcount} 条operation_type记录")
        
        # 更新status字段
        result2 = conn.execute(text(
            "UPDATE operation_logs SET status = UPPER(status)"
        ))
        print(f"更新了 {result2.rowcount} 条status记录")
        
        # 提交事务
        conn.commit()
        
        # 验证更新结果
        result = conn.execute(text(
            "SELECT operation_type, status, COUNT(*) as count FROM operation_logs GROUP BY operation_type, status"
        ))
        print("\n当前数据库中的枚举值分布:")
        for row in result:
            print(f"operation_type: {row[0]}, status: {row[1]}, count: {row[2]}")

if __name__ == "__main__":
    update_enum_values()
    print("枚举值更新完成")