"""操作日志模型"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database import Base
import enum

class OperationType(str, enum.Enum):
    """操作类型枚举"""
    RESTART = "RESTART"
    SYNC = "SYNC"

class OperationStatus(str, enum.Enum):
    """操作状态枚举"""
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    IN_PROGRESS = "IN_PROGRESS"

class OperationLog(Base):
    """操作日志表"""
    __tablename__ = "operation_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    lark_user_id = Column(String(100), index=True, nullable=False, comment="Lark用户ID")
    server_id = Column(Integer, ForeignKey("servers.id"), comment="服务器ID")
    operation_type = Column(Enum(OperationType, native_enum=False), nullable=False, comment="操作类型")
    status = Column(Enum(OperationStatus, native_enum=False), nullable=False, comment="操作状态")
    result = Column(Text, comment="操作结果")
    error_message = Column(Text, comment="错误信息")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关联关系
    server = relationship("Server", backref="operation_logs")
    
    def __repr__(self):
        return f"<OperationLog(id={self.id}, lark_user_id='{self.lark_user_id}', operation_type='{self.operation_type}', status='{self.status}')>"
