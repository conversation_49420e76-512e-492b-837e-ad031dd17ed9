// 仪表板组件 - 服务器管理
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Alert, Spinner, Badge, Form, InputGroup } from 'react-bootstrap';
import { apiService, Server } from '../services/api';

const Dashboard: React.FC = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(true);
  const [syncing, setSyncing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [serverTypeFilter, setServerTypeFilter] = useState<'all' | 'bare_metal' | 'instance'>('all');

  useEffect(() => {
    loadServers();
  }, [searchTerm, serverTypeFilter]);

  const loadServers = async () => {
    try {
      setLoading(true);
      setError('');
      const serverType = serverTypeFilter === 'all' ? undefined : serverTypeFilter;
      const search = searchTerm.trim() || undefined;
      const response = await apiService.getServers(0, 100, serverType, search);
      setServers(response.items);
    } catch (error: any) {
      console.error('加载服务器列表失败:', error);
      setError('加载服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    try {
      setSyncing(true);
      setError('');
      setSuccess('');
      
      const response = await apiService.syncServers();
      setSuccess(`同步成功，共同步 ${response.synced_count} 台服务器`);
      
      // 重新加载服务器列表
      await loadServers();
    } catch (error: any) {
      console.error('同步服务器失败:', error);
      setError('同步服务器失败');
    } finally {
      setSyncing(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    const statusMap: { [key: string]: string } = {
      'active': 'success',
      'inactive': 'secondary',
      'pending': 'warning',
      'error': 'danger'
    };

    const variant = statusMap[status?.toLowerCase() || 'unknown'] || 'secondary';

    return (
      <Badge bg={variant}>
        {status || 'Unknown'}
      </Badge>
    );
  };

  const getServerTypeBadge = (serverType: 'bare_metal' | 'instance' | undefined) => {
    const typeMap = {
      'bare_metal': { variant: 'primary', text: '裸机' },
      'instance': { variant: 'info', text: '实例' }
    };

    // 处理undefined或未知类型的情况
    const config = typeMap[serverType as keyof typeof typeMap] || { variant: 'secondary', text: '未知' };

    return (
      <Badge bg={config.variant}>
        {config.text}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  return (
    <Container className="py-4">
      <Row className="mb-4">
        <Col>
          <div className="d-flex justify-content-between align-items-center">
            <h2>
              <i className="bi bi-server me-2"></i>
              服务器管理
            </h2>
            <Button
              variant="success"
              onClick={handleSync}
              disabled={syncing}
              className="d-flex align-items-center"
            >
              {syncing ? (
                <>
                  <Spinner
                    as="span"
                    animation="border"
                    size="sm"
                    role="status"
                    className="me-2"
                  />
                  同步中...
                </>
              ) : (
                <>
                  <i className="bi bi-arrow-clockwise me-2"></i>
                  同步服务器
                </>
              )}
            </Button>
          </div>
        </Col>
      </Row>

      {/* 搜索和过滤 */}
      <Row className="mb-3">
        <Col md={6}>
          <InputGroup>
            <InputGroup.Text>
              <i className="bi bi-search"></i>
            </InputGroup.Text>
            <Form.Control
              type="text"
              placeholder="搜索服务器（IP地址、ID或标签）"
              value={searchTerm}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
            />
          </InputGroup>
        </Col>
        <Col md={3}>
          <Form.Select
            value={serverTypeFilter}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setServerTypeFilter(e.target.value as 'all' | 'bare_metal' | 'instance')}
          >
            <option value="all">所有类型</option>
            <option value="bare_metal">裸机</option>
            <option value="instance">实例</option>
          </Form.Select>
        </Col>
        <Col md={3}>
          <div className="text-muted small">
            共 {servers.length} 台服务器
          </div>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <i className="bi bi-exclamation-triangle me-2"></i>
          {error}
        </Alert>
      )}

      {success && (
        <Alert variant="success" dismissible onClose={() => setSuccess('')}>
          <i className="bi bi-check-circle me-2"></i>
          {success}
        </Alert>
      )}

      <Card className="shadow-sm">
        <Card.Body>
          {loading ? (
            <div className="text-center py-5">
              <Spinner animation="border" role="status">
                <span className="visually-hidden">加载中...</span>
              </Spinner>
              <p className="mt-2 text-muted">加载服务器列表中...</p>
            </div>
          ) : servers.length === 0 ? (
            <div className="text-center py-5">
              <i className="bi bi-inbox display-1 text-muted"></i>
              <p className="mt-3 text-muted">暂无服务器数据</p>
              <Button variant="primary" onClick={handleSync}>
                <i className="bi bi-arrow-clockwise me-2"></i>
                同步服务器
              </Button>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table striped hover>
                  <thead className="table-dark">
                    <tr>
                      <th>ID</th>
                      <th>类型</th>
                      <th>Vultr ID</th>
                      <th>IP地址</th>
                      <th>标签</th>
                      <th>状态</th>
                      <th>区域</th>
                      <th>更新时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {servers.map((server: Server) => (
                      <tr key={server.id}>
                        <td>{server.id}</td>
                        <td>{getServerTypeBadge(server.server_type)}</td>
                        <td>
                          <code className="text-primary">{server.vultr_id}</code>
                        </td>
                        <td>{server.ip_address || '-'}</td>
                        <td>{server.label || '-'}</td>
                        <td>{getStatusBadge(server.status)}</td>
                        <td>{server.region || '-'}</td>
                        <td className="text-muted">
                          {formatDate(server.updated_at)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
              
              <div className="mt-3 text-muted">
                <small>
                  <i className="bi bi-info-circle me-1"></i>
                  共 {servers.length} 台服务器
                </small>
              </div>
            </>
          )}
        </Card.Body>
      </Card>
    </Container>
  );
};

export default Dashboard;
