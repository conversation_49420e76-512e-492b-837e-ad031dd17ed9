#!/bin/bash

# 数据库管理脚本

set -e

MYSQL_CONTAINER="lark_robot_mysql"
MYSQL_USER="${MYSQL_USER:-lark_user}"
MYSQL_PASSWORD="${MYSQL_PASSWORD:-lark_pass}"
MYSQL_DATABASE="${MYSQL_DATABASE:-lark_robot}"
MYSQL_ROOT_PASSWORD="${MYSQL_ROOT_PASSWORD:-root123}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_usage() {
    echo "数据库管理脚本使用说明："
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  backup              备份数据库"
    echo "  restore <file>      从备份文件恢复数据库"
    echo "  reset               重置数据库（删除所有数据）"
    echo "  connect             连接到MySQL数据库"
    echo "  status              查看数据库状态"
    echo "  logs                查看数据库日志"
    echo "  help                显示此帮助信息"
    echo ""
}

check_mysql_container() {
    if ! docker ps | grep -q $MYSQL_CONTAINER; then
        echo -e "${RED}❌ MySQL容器未运行，请先启动服务: docker-compose up -d mysql${NC}"
        exit 1
    fi
}

backup_database() {
    check_mysql_container
    
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    echo -e "${YELLOW}🔄 正在备份数据库...${NC}"
    
    docker exec $MYSQL_CONTAINER mysqldump \
        -u root -p$MYSQL_ROOT_PASSWORD \
        --single-transaction \
        --routines \
        --triggers \
        $MYSQL_DATABASE > $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库备份成功: $BACKUP_FILE${NC}"
    else
        echo -e "${RED}❌ 数据库备份失败${NC}"
        exit 1
    fi
}

restore_database() {
    check_mysql_container
    
    if [ -z "$1" ]; then
        echo -e "${RED}❌ 请指定备份文件路径${NC}"
        exit 1
    fi
    
    BACKUP_FILE="$1"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        echo -e "${RED}❌ 备份文件不存在: $BACKUP_FILE${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}⚠️  警告: 此操作将覆盖现有数据库数据${NC}"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
    
    echo -e "${YELLOW}🔄 正在恢复数据库...${NC}"
    
    docker exec -i $MYSQL_CONTAINER mysql \
        -u root -p$MYSQL_ROOT_PASSWORD \
        $MYSQL_DATABASE < $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库恢复成功${NC}"
    else
        echo -e "${RED}❌ 数据库恢复失败${NC}"
        exit 1
    fi
}

reset_database() {
    check_mysql_container
    
    echo -e "${YELLOW}⚠️  警告: 此操作将删除所有数据库数据${NC}"
    read -p "确定要重置数据库吗？(y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        exit 0
    fi
    
    echo -e "${YELLOW}🔄 正在重置数据库...${NC}"
    
    # 删除并重新创建数据库
    docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "
        DROP DATABASE IF EXISTS $MYSQL_DATABASE;
        CREATE DATABASE $MYSQL_DATABASE CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    "
    
    # 重新执行初始化脚本
    if [ -f "backend/init.sql" ]; then
        docker exec -i $MYSQL_CONTAINER mysql \
            -u root -p$MYSQL_ROOT_PASSWORD \
            $MYSQL_DATABASE < backend/init.sql
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 数据库重置成功${NC}"
    else
        echo -e "${RED}❌ 数据库重置失败${NC}"
        exit 1
    fi
}

connect_database() {
    check_mysql_container
    
    echo -e "${GREEN}🔗 连接到MySQL数据库...${NC}"
    echo -e "${YELLOW}提示: 使用 'exit' 退出MySQL客户端${NC}"
    
    docker exec -it $MYSQL_CONTAINER mysql \
        -u $MYSQL_USER -p$MYSQL_PASSWORD \
        $MYSQL_DATABASE
}

show_status() {
    check_mysql_container
    
    echo -e "${GREEN}📊 数据库状态信息:${NC}"
    echo ""
    
    # 显示数据库基本信息
    docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "
        SELECT VERSION() as 'MySQL版本';
        SELECT DATABASE() as '当前数据库';
        SHOW TABLES;
    " $MYSQL_DATABASE
    
    echo ""
    echo -e "${GREEN}📈 表记录统计:${NC}"
    docker exec $MYSQL_CONTAINER mysql -u root -p$MYSQL_ROOT_PASSWORD -e "
        SELECT 'users' as '表名', COUNT(*) as '记录数' FROM users
        UNION ALL
        SELECT 'servers' as '表名', COUNT(*) as '记录数' FROM servers
        UNION ALL
        SELECT 'user_server_bindings' as '表名', COUNT(*) as '记录数' FROM user_server_bindings
        UNION ALL
        SELECT 'operation_logs' as '表名', COUNT(*) as '记录数' FROM operation_logs;
    " $MYSQL_DATABASE
}

show_logs() {
    echo -e "${GREEN}📋 MySQL容器日志:${NC}"
    docker logs --tail=50 -f $MYSQL_CONTAINER
}

# 主程序
case "$1" in
    backup)
        backup_database
        ;;
    restore)
        restore_database "$2"
        ;;
    reset)
        reset_database
        ;;
    connect)
        connect_database
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    help|--help|-h)
        print_usage
        ;;
    *)
        echo -e "${RED}❌ 无效的选项: $1${NC}"
        echo ""
        print_usage
        exit 1
        ;;
esac



