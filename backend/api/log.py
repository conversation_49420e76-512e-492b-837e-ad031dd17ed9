"""操作日志API路由"""
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session

from database import get_db
from schemas.log import OperationLogListResponse
from services.server_service import ServerService
from api.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=OperationLogListResponse)
async def get_operation_logs(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取操作日志列表"""
    logs, total = ServerService.get_operation_logs(db, skip=skip, limit=limit)
    
    # 转换为响应格式
    log_items = []
    for log in logs:
        # 确保枚举值正确转换
        operation_type = log.operation_type
        if isinstance(operation_type, str):
            # 如果是字符串，转换为对应的枚举
            from models.log import OperationType, OperationStatus
            operation_type = OperationType(operation_type)
        
        status = log.status
        if isinstance(status, str):
            status = OperationStatus(status)
        
        log_dict = {
            "id": log.id,
            "lark_user_id": log.lark_user_id,
            "server_id": log.server_id,
            "operation_type": operation_type,
            "status": status,
            "result": log.result,
            "error_message": log.error_message,
            "created_at": log.created_at,
            "server": {
                "id": log.server.id,
                "vultr_id": log.server.vultr_id,
                "ip_address": log.server.ip_address,
                "label": log.server.label
            } if log.server else None
        }
        log_items.append(log_dict)
    
    return {
        "total": total,
        "items": log_items
    }
