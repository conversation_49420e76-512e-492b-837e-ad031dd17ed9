-- 添加服务器类型字段的数据库迁移脚本
-- 执行时间: 2025-09-12

-- 添加server_type字段到servers表
ALTER TABLE servers 
ADD COLUMN server_type ENUM('bare_metal', 'instance') NOT NULL DEFAULT 'bare_metal' 
COMMENT '服务器类型' 
AFTER vultr_id;

-- 更新现有数据，将所有现有服务器标记为裸机类型（因为之前只支持裸机）
UPDATE servers SET server_type = 'bare_metal' WHERE server_type IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_servers_type ON servers(server_type);

-- 显示更新后的表结构
DESCRIBE servers;

-- 显示更新的记录数
SELECT 
    server_type,
    COUNT(*) as count
FROM servers 
GROUP BY server_type;
