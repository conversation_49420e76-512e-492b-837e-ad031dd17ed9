"""服务器模型"""
from sqlalchemy import Column, Integer, String, DateTime, Text, Enum
from sqlalchemy.sql import func
from database import Base
import enum

class ServerType(enum.Enum):
    """服务器类型枚举"""
    BARE_METAL = "BARE_METAL"
    INSTANCE = "INSTANCE"

class Server(Base):
    """Vultr服务器表（包括裸机和实例）"""
    __tablename__ = "servers"

    id = Column(Integer, primary_key=True, index=True)
    vultr_id = Column(String(50), unique=True, index=True, nullable=False, comment="Vultr服务器ID")
    server_type = Column(Enum(ServerType), nullable=False, default=ServerType.BARE_METAL, comment="服务器类型")
    ip_address = Column(String(15), index=True, comment="服务器IP地址")
    label = Column(String(100), comment="服务器标签")
    status = Column(String(20), comment="服务器状态")
    region = Column(String(50), comment="服务器区域")
    os = Column(String(100), comment="操作系统")
    plan = Column(String(50), comment="服务器套餐")
    raw_data = Column(Text, comment="原始数据JSON")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<Server(id={self.id}, vultr_id='{self.vultr_id}', type='{self.server_type.value}', ip='{self.ip_address}')>"
