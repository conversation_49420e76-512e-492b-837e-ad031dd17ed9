-- MySQL初始化脚本
-- 此脚本会在MySQL容器首次启动时自动执行

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `lark_robot` 
DEFAULT CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE `lark_robot`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE,
  `email` varchar(100) DEFAULT NULL,
  `hashed_password` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_users_username` (`username`),
  KEY `ix_users_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建服务器表
CREATE TABLE IF NOT EXISTS `servers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `vultr_id` varchar(50) NOT NULL UNIQUE,
  `ip_address` varchar(45) DEFAULT NULL,
  `label` varchar(100) DEFAULT NULL,
  `status` varchar(20) DEFAULT NULL,
  `region` varchar(50) DEFAULT NULL,
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `plan` varchar(50) DEFAULT NULL COMMENT '服务器套餐',
  `raw_data` text DEFAULT NULL COMMENT '原始数据JSON',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ix_servers_vultr_id` (`vultr_id`),
  KEY `ix_servers_ip_address` (`ip_address`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户服务器绑定表
CREATE TABLE IF NOT EXISTS `user_server_bindings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `lark_user_id` varchar(100) NOT NULL COMMENT 'Lark用户ID',
  `server_id` int NOT NULL COMMENT '服务器ID',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_server_binding` (`lark_user_id`, `server_id`),
  KEY `ix_user_server_bindings_lark_user_id` (`lark_user_id`),
  KEY `fk_user_server_bindings_server_id` (`server_id`),
  CONSTRAINT `fk_user_server_bindings_server_id` FOREIGN KEY (`server_id`) REFERENCES `servers` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建操作日志表
CREATE TABLE IF NOT EXISTS `operation_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `lark_user_id` varchar(100) NOT NULL COMMENT 'Lark用户ID',
  `server_id` int DEFAULT NULL,
  `operation_type` enum('RESTART','SYNC') NOT NULL,
  `status` enum('SUCCESS','FAILED','IN_PROGRESS') NOT NULL,
  `result` text,
  `error_message` text,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `ix_operation_logs_lark_user_id` (`lark_user_id`),
  KEY `ix_operation_logs_created_at` (`created_at`),
  KEY `fk_operation_logs_server_id` (`server_id`),
  CONSTRAINT `fk_operation_logs_server_id` FOREIGN KEY (`server_id`) REFERENCES `servers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员用户
-- 密码: admin123 (使用bcrypt加密)
INSERT IGNORE INTO `users` (`username`, `email`, `hashed_password`, `is_active`) 
VALUES (
  'admin', 
  '<EMAIL>', 
  '$2b$12$5jbgkRlxkUKvMcKdLBVG5e26d.1C0Z2WW27pqbfMmf9wXF1KQ09cW', 
  1
);

-- 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示创建的表
SHOW TABLES;

