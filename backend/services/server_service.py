"""服务器服务"""
import json

from sqlalchemy.orm import Session
from typing import List, Optional, Tuple
from models.server import Server, ServerType
from models.binding import UserServerBinding
from models.log import OperationLog, OperationType, OperationStatus
from schemas.server import ServerCreate, ServerUpdate
from services.vultr_service import vultr_service
from utils.validators import parse_server_input
from utils.logger import app_logger

class ServerService:
    """服务器服务类"""
    
    @staticmethod
    async def sync_servers_from_vultr(db: Session) -> int:
        """从Vultr同步服务器信息（包括裸机和实例）"""
        try:
            synced_count = 0

            # 同步裸机服务器
            app_logger.info("开始同步Vultr裸机服务器...")
            bare_metals = await vultr_service.list_bare_metals()
            synced_count += await ServerService._sync_servers_by_type(
                db, bare_metals, ServerType.BARE_METAL, "main_ip"
            )

            # 同步VPS实例
            app_logger.info("开始同步Vultr VPS实例...")
            instances = await vultr_service.list_instances()
            synced_count += await ServerService._sync_servers_by_type(
                db, instances, ServerType.INSTANCE, "main_ip"
            )

            db.commit()
            app_logger.info(f"成功同步 {synced_count} 台服务器信息（裸机 + 实例）")
            return synced_count

        except Exception as e:
            app_logger.error(f"同步服务器信息失败: {e}")
            db.rollback()
            return 0

    @staticmethod
    async def _sync_servers_by_type(db: Session, servers: List[dict], server_type: ServerType, ip_field: str) -> int:
        """按类型同步服务器"""
        synced_count = 0

        for server in servers:
            vultr_id = server.get("id")
            if not vultr_id:
                continue

            # 检查服务器是否已存在
            existing_server = db.query(Server).filter(Server.vultr_id == vultr_id).first()

            server_data = {
                "vultr_id": vultr_id,
                "server_type": server_type,
                "ip_address": server.get(ip_field),
                "label": server.get("label"),
                "status": server.get("status"),
                "region": server.get("region"),
                "os": server.get("os"),
                "plan": server.get("plan"),
                "raw_data": json.dumps(server)
            }

            if existing_server:
                # 更新现有服务器
                for key, value in server_data.items():
                    if key != "vultr_id":  # 不更新vultr_id
                        setattr(existing_server, key, value)
            else:
                # 创建新服务器
                new_server = Server(**server_data)
                db.add(new_server)

            synced_count += 1

        app_logger.info(f"同步了 {synced_count} 台 {server_type.value} 服务器")
        return synced_count
    
    @staticmethod
    def get_server_by_input(db: Session, server_input: str) -> Optional[Server]:
        """根据用户输入获取服务器"""
        input_type, value = parse_server_input(server_input)
        
        if input_type == "ip":
            return db.query(Server).filter(Server.ip_address == value).first()
        elif input_type == "id":
            return db.query(Server).filter(Server.vultr_id == value).first()
        else:
            return None
    
    @staticmethod
    def check_user_server_permission(db: Session, lark_user_id: str, server_id: int) -> bool:
        """检查用户是否有服务器权限"""
        binding = db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id,
            UserServerBinding.server_id == server_id
        ).first()
        return binding is not None
    
    @staticmethod
    def create_user_server_binding(db: Session, lark_user_id: str, server_id: int) -> UserServerBinding:
        """创建用户服务器绑定关系"""
        # 检查是否已存在绑定关系
        existing_binding = db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id,
            UserServerBinding.server_id == server_id
        ).first()
        
        if existing_binding:
            return existing_binding
        
        binding = UserServerBinding(
            lark_user_id=lark_user_id,
            server_id=server_id
        )
        db.add(binding)
        db.commit()
        db.refresh(binding)
        return binding
    
    @staticmethod
    def get_user_bindings(db: Session, lark_user_id: str) -> List[UserServerBinding]:
        """获取用户的服务器绑定关系"""
        return db.query(UserServerBinding).filter(
            UserServerBinding.lark_user_id == lark_user_id
        ).all()
    
    @staticmethod
    def delete_user_server_binding(db: Session, binding_id: int) -> bool:
        """删除用户服务器绑定关系"""
        binding = db.query(UserServerBinding).filter(UserServerBinding.id == binding_id).first()
        if binding:
            db.delete(binding)
            db.commit()
            return True
        return False
    
    @staticmethod
    def create_operation_log(db: Session, lark_user_id: str, server_id: Optional[int], 
                           operation_type: OperationType, status: OperationStatus,
                           result: Optional[str] = None, error_message: Optional[str] = None) -> OperationLog:
        """创建操作日志"""
        log = OperationLog(
            lark_user_id=lark_user_id,
            server_id=server_id,
            operation_type=operation_type,
            status=status,
            result=result,
            error_message=error_message
        )
        db.add(log)
        db.commit()
        db.refresh(log)
        return log
    
    @staticmethod
    def get_servers(db: Session, skip: int = 0, limit: int = 100,
                   server_type: Optional[ServerType] = None,
                   search: Optional[str] = None) -> Tuple[List[Server], int]:
        """获取服务器列表"""
        query = db.query(Server)

        # 按服务器类型过滤
        if server_type:
            query = query.filter(Server.server_type == server_type)

        # 搜索功能（IP地址或ID模糊匹配）
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Server.ip_address.like(search_term)) |
                (Server.vultr_id.like(search_term)) |
                (Server.label.like(search_term))
            )

        total = query.count()
        servers = query.offset(skip).limit(limit).all()
        return servers, total
    
    @staticmethod
    def get_operation_logs(db: Session, skip: int = 0, limit: int = 100) -> Tuple[List[OperationLog], int]:
        """获取操作日志列表"""
        total = db.query(OperationLog).count()
        logs = db.query(OperationLog).order_by(OperationLog.created_at.desc()).offset(skip).limit(limit).all()
        return logs, total
