"""日志相关的Pydantic模型"""
from pydantic import BaseModel, field_validator
from typing import Optional, List, Union
from datetime import datetime
from models.log import OperationType, OperationStatus

class OperationLogCreate(BaseModel):
    """创建操作日志"""
    lark_user_id: str
    server_id: Optional[int] = None
    operation_type: OperationType
    status: OperationStatus
    result: Optional[str] = None
    error_message: Optional[str] = None
    
    @field_validator('operation_type', mode='before')
    @classmethod
    def validate_operation_type(cls, v: Union[str, OperationType]) -> OperationType:
        """验证并转换操作类型枚举"""
        if isinstance(v, str):
            return OperationType(v)
        return v
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v: Union[str, OperationStatus]) -> OperationStatus:
        """验证并转换操作状态枚举"""
        if isinstance(v, str):
            return OperationStatus(v)
        return v

class OperationLogResponse(BaseModel):
    """操作日志响应"""
    id: int
    lark_user_id: str
    server_id: Optional[int] = None
    operation_type: OperationType
    status: OperationStatus
    result: Optional[str] = None
    error_message: Optional[str] = None
    created_at: datetime
    server: Optional[dict] = None
    
    @field_validator('operation_type', mode='before')
    @classmethod
    def validate_operation_type(cls, v: Union[str, OperationType]) -> OperationType:
        """验证并转换操作类型枚举"""
        if isinstance(v, str):
            return OperationType(v)
        return v
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v: Union[str, OperationStatus]) -> OperationStatus:
        """验证并转换操作状态枚举"""
        if isinstance(v, str):
            return OperationStatus(v)
        return v
    
    class Config:
        from_attributes = True

class OperationLogListResponse(BaseModel):
    """操作日志列表响应"""
    total: int
    items: List[OperationLogResponse]
